["\\\\?\\C:\\Users\\<USER>\\filter\\src-tauri\\target\\debug\\build\\tauri-efb8300a8907c154\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\C:\\Users\\<USER>\\filter\\src-tauri\\target\\debug\\build\\tauri-efb8300a8907c154\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\C:\\Users\\<USER>\\filter\\src-tauri\\target\\debug\\build\\tauri-efb8300a8907c154\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\C:\\Users\\<USER>\\filter\\src-tauri\\target\\debug\\build\\tauri-efb8300a8907c154\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\C:\\Users\\<USER>\\filter\\src-tauri\\target\\debug\\build\\tauri-efb8300a8907c154\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\C:\\Users\\<USER>\\filter\\src-tauri\\target\\debug\\build\\tauri-efb8300a8907c154\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\C:\\Users\\<USER>\\filter\\src-tauri\\target\\debug\\build\\tauri-efb8300a8907c154\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\C:\\Users\\<USER>\\filter\\src-tauri\\target\\debug\\build\\tauri-efb8300a8907c154\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\C:\\Users\\<USER>\\filter\\src-tauri\\target\\debug\\build\\tauri-efb8300a8907c154\\out\\permissions\\path\\autogenerated\\default.toml"]