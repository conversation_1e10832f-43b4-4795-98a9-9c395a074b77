{"rustc": 16591470773350601817, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 1208793506481205622, "deps": [[4899080583175475170, "semver", false, 13379671138453032240], [6913375703034175521, "schemars", false, 14141193988605639260], [7170110829644101142, "json_patch", false, 4390126803911236291], [8786711029710048183, "toml", false, 1286918838789226014], [9689903380558560274, "serde", false, 11661074885678527245], [11050281405049894993, "tauri_utils", false, 2131521612342736601], [12714016054753183456, "tauri_winres", false, 1068357628820295487], [13077543566650298139, "heck", false, 9932253229050198272], [13475171727366188400, "cargo_toml", false, 10686498359849782280], [13625485746686963219, "anyhow", false, 17767479543650335812], [15367738274754116744, "serde_json", false, 15109740685383870006], [15622660310229662834, "walkdir", false, 5991354171415335250], [16928111194414003569, "dirs", false, 4947733103275936009], [17155886227862585100, "glob", false, 8732721763287826903]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-7d60f55c8930ba81\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}