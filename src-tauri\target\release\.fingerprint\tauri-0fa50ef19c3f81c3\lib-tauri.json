{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 15039773433017454791, "deps": [[40386456601120721, "percent_encoding", false, 12705104258894253192], [442785307232013896, "tauri_runtime", false, 7090284939779752545], [1200537532907108615, "url<PERSON><PERSON>n", false, 4387919058806554277], [3150220818285335163, "url", false, 11680100545599723788], [4143744114649553716, "raw_window_handle", false, 16858575294402205335], [4341921533227644514, "muda", false, 12814990076149746531], [4919829919303820331, "serialize_to_javascript", false, 14150898785452162642], [5986029879202738730, "log", false, 4169821235999143190], [7752760652095876438, "tauri_runtime_wry", false, 1931032746823680010], [8539587424388551196, "webview2_com", false, 9556832586308886605], [9010263965687315507, "http", false, 2135897793847396854], [9228235415475680086, "tauri_macros", false, 2326506641313617866], [9538054652646069845, "tokio", false, 6931601016450391210], [9689903380558560274, "serde", false, 10973099492126875780], [9920160576179037441, "getrandom", false, 4977461337788825965], [10229185211513642314, "mime", false, 4691886428118288115], [10629569228670356391, "futures_util", false, 13852107563252293888], [10755362358622467486, "build_script_build", false, 6802830090495295321], [10806645703491011684, "thiserror", false, 10061010583523331582], [11050281405049894993, "tauri_utils", false, 730899839960654177], [11989259058781683633, "dunce", false, 4578177872343112956], [12565293087094287914, "window_vibrancy", false, 11175144866134257141], [12986574360607194341, "serde_repr", false, 13624661074339100438], [13077543566650298139, "heck", false, 7955041252151601739], [13116089016666501665, "windows", false, 1210766391554008501], [13625485746686963219, "anyhow", false, 7935982357160082222], [15367738274754116744, "serde_json", false, 17112647126500908223], [16928111194414003569, "dirs", false, 6557863424394576284], [17155886227862585100, "glob", false, 11831047265987967079]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-0fa50ef19c3f81c3\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}