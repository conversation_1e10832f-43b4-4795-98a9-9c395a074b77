{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 7693503214441230297, "deps": [[442785307232013896, "build_script_build", false, 16935620938591395253], [3150220818285335163, "url", false, 8166888441636404885], [4143744114649553716, "raw_window_handle", false, 9398976728429290834], [7606335748176206944, "dpi", false, 13822268073128535136], [9010263965687315507, "http", false, 11650626357901385786], [9689903380558560274, "serde", false, 15479814090323490116], [10806645703491011684, "thiserror", false, 5489843559512658793], [11050281405049894993, "tauri_utils", false, 14078234700842705457], [13116089016666501665, "windows", false, 1127403627518652641], [15367738274754116744, "serde_json", false, 1540098585682478347], [16727543399706004146, "cookie", false, 12462264959215625846]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-86486154476bdacd\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}