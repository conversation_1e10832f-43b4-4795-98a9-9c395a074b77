{"rustc": 16591470773350601817, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 2479614408773809198, "deps": [[561782849581144631, "html5ever", false, 13955287769636829459], [1200537532907108615, "url<PERSON><PERSON>n", false, 8924936900350205512], [3060637413840920116, "proc_macro2", false, 9296082083372779404], [3129130049864710036, "memchr", false, 3954374222536535122], [3150220818285335163, "url", false, 4440404820658173679], [3191507132440681679, "serde_untagged", false, 9271301183277290906], [4899080583175475170, "semver", false, 13379671138453032240], [5986029879202738730, "log", false, 18140071090976075314], [6213549728662707793, "serde_with", false, 2678282664828780196], [6262254372177975231, "kuchiki", false, 12861703319453749563], [6606131838865521726, "ctor", false, 7922141426268282587], [6913375703034175521, "schemars", false, 14141193988605639260], [7170110829644101142, "json_patch", false, 4390126803911236291], [8319709847752024821, "uuid", false, 4148529447461361812], [8786711029710048183, "toml", false, 1286918838789226014], [9010263965687315507, "http", false, 11650626357901385786], [9451456094439810778, "regex", false, 17067444799312657756], [9689903380558560274, "serde", false, 11661074885678527245], [10806645703491011684, "thiserror", false, 5489843559512658793], [11655476559277113544, "cargo_metadata", false, 4782304045240448302], [11989259058781683633, "dunce", false, 17502845036177328437], [13625485746686963219, "anyhow", false, 17767479543650335812], [14132538657330703225, "brotli", false, 7101722016143646241], [15367738274754116744, "serde_json", false, 15109740685383870006], [15622660310229662834, "walkdir", false, 5991354171415335250], [17146114186171651583, "infer", false, 8630026759854231208], [17155886227862585100, "glob", false, 8732721763287826903], [17186037756130803222, "phf", false, 5006025986051616693], [17990358020177143287, "quote", false, 12946586837458883424]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-2d6db6a9283aeacd\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}