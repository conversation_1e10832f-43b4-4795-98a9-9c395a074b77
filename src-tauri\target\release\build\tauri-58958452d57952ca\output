cargo:rustc-check-cfg=cfg(custom_protocol)
cargo:rustc-cfg=custom_protocol
cargo:rustc-check-cfg=cfg(dev)
cargo:dev=false
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-2.5.1\scripts\bundle.global.js
cargo:core:path__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-path-permission-files
cargo:core:event__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-event-permission-files
cargo:core:window__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-window-permission-files
cargo:core:webview__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-webview-permission-files
cargo:core:app__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-app-permission-files
cargo:core:image__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-image-permission-files
cargo:core:resources__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-resources-permission-files
cargo:core:menu__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-menu-permission-files
cargo:core:tray__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-tray-permission-files
cargo:core__CORE_PLUGIN___PERMISSION_FILES_PATH=C:\Users\<USER>\filter\src-tauri\target\release\build\tauri-58958452d57952ca\out\tauri-core-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
