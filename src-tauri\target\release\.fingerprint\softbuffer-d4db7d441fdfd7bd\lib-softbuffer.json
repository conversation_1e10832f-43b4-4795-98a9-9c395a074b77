{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2040997289075261528, "path": 1678142074430257323, "deps": [[376837177317575824, "build_script_build", false, 12850748164268146863], [4143744114649553716, "raw_window_handle", false, 16858575294402205335], [5986029879202738730, "log", false, 4169821235999143190], [10281541584571964250, "windows_sys", false, 10077964962093526301]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-d4db7d441fdfd7bd\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}