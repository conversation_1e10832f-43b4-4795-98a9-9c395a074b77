{"rustc": 16591470773350601817, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 6316683817268825358, "deps": [[3060637413840920116, "proc_macro2", false, 4450968399663963366], [7341521034400937459, "tauri_codegen", false, 7473290582181810531], [11050281405049894993, "tauri_utils", false, 3135135870016289477], [13077543566650298139, "heck", false, 17558570077769581464], [17990358020177143287, "quote", false, 7029536075333856339], [18149961000318489080, "syn", false, 9988623008240926651]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-c2f832778a48d696\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}