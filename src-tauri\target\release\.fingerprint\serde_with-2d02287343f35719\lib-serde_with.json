{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5456902659710135487, "path": 15196251422187878305, "deps": [[6158493786865284961, "serde_with_macros", false, 7665336023447846580], [9689903380558560274, "serde", false, 10973099492126875780], [16257276029081467297, "serde_derive", false, 6644484746173561585]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\serde_with-2d02287343f35719\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}