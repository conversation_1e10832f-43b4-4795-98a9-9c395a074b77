{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 7693503214441230297, "deps": [[442785307232013896, "build_script_build", false, 10212014731620285824], [3150220818285335163, "url", false, 11680100545599723788], [4143744114649553716, "raw_window_handle", false, 16858575294402205335], [7606335748176206944, "dpi", false, 14533147889796382176], [9010263965687315507, "http", false, 2135897793847396854], [9689903380558560274, "serde", false, 10973099492126875780], [10806645703491011684, "thiserror", false, 10061010583523331582], [11050281405049894993, "tauri_utils", false, 730899839960654177], [13116089016666501665, "windows", false, 1210766391554008501], [15367738274754116744, "serde_json", false, 17112647126500908223], [16727543399706004146, "cookie", false, 16968571036545033352]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-bf09e365e20c56c7\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}