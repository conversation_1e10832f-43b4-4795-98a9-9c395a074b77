{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 1501858700827923432, "profile": 17672942494452627365, "path": 10763286916239946207, "deps": [[889364689474263285, "reqwest", false, 5775888319156347887], [2716489052144243376, "build_script_build", false, 2690894777940254372], [3150220818285335163, "url", false, 3928430153611395950], [3935545708480822364, "tauri_plugin_opener", false, 15209947063941404417], [9538054652646069845, "tokio", false, 9298280961105875738], [9689903380558560274, "serde", false, 14367484472656307488], [9897246384292347999, "chrono", false, 13249408679176131417], [10755362358622467486, "tauri", false, 15863480677988692578], [15367738274754116744, "serde_json", false, 7955921909107714376]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\filter-f4636de2632543c0\\dep-lib-filter_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}