{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2716489052144243376, "build_script_build", false, 12144464827451397723], [10755362358622467486, "build_script_build", false, 11618994008688478091], [3935545708480822364, "build_script_build", false, 14529809545369880823]], "local": [{"RerunIfChanged": {"output": "debug\\build\\filter-cee609b62cf849c7\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}