<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="/src/styles.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Web Browser</title>
    <script type="module" src="/src/main.ts" defer></script>
  </head>

  <body>
    <div class="browser-container">
      <!-- 브라우저 컨트롤 영역 -->
      <div class="browser-controls">
        <div class="navigation-buttons">
          <button id="back-button" title="Back">◀</button>
          <button id="forward-button" title="Forward">▶</button>
          <button id="refresh-button" title="Refresh">↻</button>
          <button id="home-button" title="Home">🏠</button>
        </div>

        <div class="address-bar">
          <input type="text" id="url-input" placeholder="Enter URL..." />
          <button id="go-button">Go</button>
        </div>

        <div class="browser-actions">
          <button id="bookmark-button" title="Bookmark">⭐</button>
          <button id="bookmarks-list-button" title="Bookmarks">📚</button>
          <button id="history-button" title="History">🕒</button>
        </div>
      </div>

      <!-- 탭 영역 -->
      <div class="tabs-container">
        <div class="tabs" id="tabs">
          <div class="tab active" data-tab-id="tab-1">New Tab</div>
          <button id="new-tab-button">+</button>
        </div>
      </div>

      <!-- 웹뷰 영역 -->
      <div class="webview-container">
        <div id="loading-indicator">Loading...</div>
        <div id="webview-placeholder">
          <!-- 웹뷰가 로드되기 전 표시될 내용 -->
          <div class="welcome-screen">
            <h1>Welcome to Your Web Browser</h1>
            <p class="browser-info">This browser opens websites in new windows within the app for a seamless browsing experience.</p>
            <div class="quick-links">
              <h2>Quick Links</h2>
              <ul>
                <li><a href="#" class="quick-link" data-url="https://www.google.com">Google</a></li>
                <li><a href="#" class="quick-link" data-url="https://www.github.com">GitHub</a></li>
                <li><a href="#" class="quick-link" data-url="https://www.youtube.com">YouTube</a></li>
                <li><a href="#" class="quick-link" data-url="https://www.wikipedia.org">Wikipedia</a></li>
              </ul>
            </div>
            <div class="instructions">
              <h3>How to use:</h3>
              <ul>
                <li>Enter a URL in the address bar and press Enter or click "Go"</li>
                <li>Click on any quick link above to open in a new window</li>
                <li>Use the bookmark button (⭐) to save your favorite sites</li>
                <li>View your browsing history with the history button (🕒)</li>
                <li>Each website opens in its own window for better organization</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 북마크 패널 -->
      <div id="bookmarks-panel" class="panel">
        <div class="panel-header">
          <h2>Bookmarks</h2>
          <button id="close-bookmarks-panel">×</button>
        </div>
        <div id="bookmarks-list" class="panel-content">
          <!-- 북마크 목록이 여기에 표시됩니다 -->
        </div>
      </div>

      <!-- 히스토리 패널 -->
      <div id="history-panel" class="panel">
        <div class="panel-header">
          <h2>History</h2>
          <button id="close-history-panel">×</button>
        </div>
        <div id="history-list" class="panel-content">
          <!-- 히스토리 목록이 여기에 표시됩니다 -->
        </div>
      </div>
    </div>
  </body>
</html>
