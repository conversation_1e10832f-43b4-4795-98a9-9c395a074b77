{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 6316683817268825358, "deps": [[3060637413840920116, "proc_macro2", false, 9296082083372779404], [7341521034400937459, "tauri_codegen", false, 5964847659543829517], [11050281405049894993, "tauri_utils", false, 2131521612342736601], [13077543566650298139, "heck", false, 9932253229050198272], [17990358020177143287, "quote", false, 12946586837458883424], [18149961000318489080, "syn", false, 634113987737082460]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-a482ad5d9c82b0ec\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}