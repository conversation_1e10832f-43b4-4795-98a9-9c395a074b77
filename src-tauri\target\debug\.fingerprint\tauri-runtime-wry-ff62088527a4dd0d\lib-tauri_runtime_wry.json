{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 7219169972497691055, "deps": [[376837177317575824, "softbuffer", false, 8098344640818423861], [442785307232013896, "tauri_runtime", false, 15999656449218192114], [3150220818285335163, "url", false, 8166888441636404885], [3722963349756955755, "once_cell", false, 7084878074372671802], [4143744114649553716, "raw_window_handle", false, 9398976728429290834], [5986029879202738730, "log", false, 18140071090976075314], [7752760652095876438, "build_script_build", false, 3413826198947120161], [8539587424388551196, "webview2_com", false, 3052557753397640431], [9010263965687315507, "http", false, 11650626357901385786], [11050281405049894993, "tauri_utils", false, 14078234700842705457], [13116089016666501665, "windows", false, 1127403627518652641], [13223659721939363523, "tao", false, 366351558313223973], [14794439852947137341, "wry", false, 9413514351224248739]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-ff62088527a4dd0d\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}