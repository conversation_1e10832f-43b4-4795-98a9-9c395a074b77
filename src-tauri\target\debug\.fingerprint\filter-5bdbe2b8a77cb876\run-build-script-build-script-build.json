{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2716489052144243376, "build_script_build", false, 14362429827489183067], [10755362358622467486, "build_script_build", false, 6862920952192178017], [3935545708480822364, "build_script_build", false, 15444233674164339014]], "local": [{"RerunIfChanged": {"output": "debug\\build\\filter-5bdbe2b8a77cb876\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}