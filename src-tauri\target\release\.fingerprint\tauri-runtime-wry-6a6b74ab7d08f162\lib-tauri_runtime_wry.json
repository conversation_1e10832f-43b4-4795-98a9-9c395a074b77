{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 7219169972497691055, "deps": [[376837177317575824, "softbuffer", false, 14492803686403312735], [442785307232013896, "tauri_runtime", false, 7090284939779752545], [3150220818285335163, "url", false, 11680100545599723788], [3722963349756955755, "once_cell", false, 1043277788669730659], [4143744114649553716, "raw_window_handle", false, 16858575294402205335], [5986029879202738730, "log", false, 4169821235999143190], [7752760652095876438, "build_script_build", false, 366215161579129043], [8539587424388551196, "webview2_com", false, 9556832586308886605], [9010263965687315507, "http", false, 2135897793847396854], [11050281405049894993, "tauri_utils", false, 730899839960654177], [13116089016666501665, "windows", false, 1210766391554008501], [13223659721939363523, "tao", false, 11135336171015092147], [14794439852947137341, "wry", false, 6150730059316027520]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-6a6b74ab7d08f162\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}