{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 5895134450075589501, "deps": [[1615478164327904835, "pin_utils", false, 87449057363141504], [1906322745568073236, "pin_project_lite", false, 2557037301034435374], [6955678925937229351, "slab", false, 17194915146941213270], [7620660491849607393, "futures_core", false, 6663968013209208852], [10565019901765856648, "futures_macro", false, 16817185850826814078], [16240732885093539806, "futures_task", false, 12246067718906832331]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-6b09cb510dee0609\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}