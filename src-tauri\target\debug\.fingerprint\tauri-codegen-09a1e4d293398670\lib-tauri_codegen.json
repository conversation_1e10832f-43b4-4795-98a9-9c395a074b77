{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 18268707459763164491, "deps": [[3060637413840920116, "proc_macro2", false, 9296082083372779404], [3150220818285335163, "url", false, 4440404820658173679], [4899080583175475170, "semver", false, 13379671138453032240], [7170110829644101142, "json_patch", false, 4390126803911236291], [7392050791754369441, "ico", false, 12211818101034736828], [8319709847752024821, "uuid", false, 4148529447461361812], [9689903380558560274, "serde", false, 11661074885678527245], [9857275760291862238, "sha2", false, 4408166586543360852], [10806645703491011684, "thiserror", false, 5489843559512658793], [11050281405049894993, "tauri_utils", false, 2131521612342736601], [12687914511023397207, "png", false, 13910146567929165582], [13077212702700853852, "base64", false, 9604072203701515246], [14132538657330703225, "brotli", false, 7101722016143646241], [15367738274754116744, "serde_json", false, 15109740685383870006], [15622660310229662834, "walkdir", false, 5991354171415335250], [17990358020177143287, "quote", false, 12946586837458883424], [18149961000318489080, "syn", false, 634113987737082460]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-09a1e4d293398670\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}