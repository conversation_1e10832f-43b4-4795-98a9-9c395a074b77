:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;

  color: #0f0f0f;
  background-color: #f6f6f6;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}

/* 브라우저 컨테이너 */
.browser-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}

/* 브라우저 컨트롤 영역 */
.browser-controls {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #e8e8e8;
  border-bottom: 1px solid #ccc;
  gap: 12px;
}

.navigation-buttons {
  display: flex;
  gap: 4px;
}

.navigation-buttons button {
  padding: 6px 10px;
  border: 1px solid #ccc;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.navigation-buttons button:hover {
  background-color: #f0f0f0;
}

.navigation-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.address-bar {
  flex: 1;
  display: flex;
  gap: 8px;
}

#url-input {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

#go-button {
  padding: 6px 16px;
  border: 1px solid #ccc;
  background-color: #007acc;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

#go-button:hover {
  background-color: #005a9e;
}

.browser-actions {
  display: flex;
  gap: 4px;
}

.browser-actions button {
  padding: 6px 10px;
  border: 1px solid #ccc;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.browser-actions button:hover {
  background-color: #f0f0f0;
}

/* 탭 영역 */
.tabs-container {
  background-color: #d8d8d8;
  border-bottom: 1px solid #ccc;
}

.tabs {
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.tab {
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  cursor: pointer;
  margin-right: 2px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tab.active {
  background-color: #fff;
  border-bottom: 1px solid #fff;
}

.tab:hover {
  background-color: #e8e8e8;
}

.tab.active:hover {
  background-color: #fff;
}

#new-tab-button {
  padding: 6px 12px;
  border: 1px solid #ccc;
  background-color: #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 8px;
}

#new-tab-button:hover {
  background-color: #e0e0e0;
}

/* 웹뷰 영역 */
.webview-container {
  flex: 1;
  position: relative;
  background-color: #fff;
}

#loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: none;
  font-size: 16px;
  color: #666;
}

#webview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-screen {
  text-align: center;
  max-width: 600px;
  padding: 40px;
}

.welcome-screen h1 {
  margin-bottom: 20px;
  color: #333;
}

.browser-info {
  margin-bottom: 30px;
  color: #666;
  font-style: italic;
}

.quick-links h2 {
  margin-bottom: 20px;
  color: #555;
}

.quick-links ul {
  list-style: none;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.quick-links li {
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  transition: background-color 0.2s;
}

.quick-links li:hover {
  background-color: #e8e8e8;
}

.quick-link {
  text-decoration: none;
  color: #007acc;
  font-weight: 500;
  display: block;
}

.quick-link:hover {
  color: #005a9e;
}

.instructions {
  margin-top: 40px;
  text-align: left;
  max-width: 500px;
}

.instructions h3 {
  margin-bottom: 15px;
  color: #333;
}

.instructions ul {
  list-style: disc;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
  color: #666;
}

#webview {
  width: 100%;
  height: 100%;
  border: none;
  background-color: #fff;
}

/* 패널 스타일 */
.panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background-color: #fff;
  border-left: 1px solid #ccc;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 1000;
}

.panel.open {
  right: 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #ccc;
}

.panel-header h2 {
  margin: 0;
  font-size: 18px;
}

.panel-header button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
}

.panel-content {
  padding: 16px;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.bookmark-item,
.history-item {
  padding: 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.bookmark-item:hover,
.history-item:hover {
  background-color: #f8f8f8;
}

.bookmark-title,
.history-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.bookmark-url,
.history-url {
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

.history-time {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
}

@media (prefers-color-scheme: dark) {
  :root {
    color: #f6f6f6;
    background-color: #2f2f2f;
  }

  .browser-controls {
    background-color: #3a3a3a;
    border-bottom-color: #555;
  }

  .navigation-buttons button,
  .browser-actions button {
    background-color: #4a4a4a;
    border-color: #666;
    color: #fff;
  }

  .navigation-buttons button:hover,
  .browser-actions button:hover {
    background-color: #5a5a5a;
  }

  #url-input {
    background-color: #4a4a4a;
    border-color: #666;
    color: #fff;
  }

  .tabs-container {
    background-color: #3a3a3a;
    border-bottom-color: #555;
  }

  .tab {
    background-color: #4a4a4a;
    border-color: #666;
    color: #fff;
  }

  .tab.active {
    background-color: #2f2f2f;
  }

  .webview-container {
    background-color: #2f2f2f;
  }

  .welcome-screen h1 {
    color: #f6f6f6;
  }

  .browser-info {
    color: #aaa;
  }

  .quick-links h2 {
    color: #ddd;
  }

  .instructions h3 {
    color: #f6f6f6;
  }

  .instructions li {
    color: #aaa;
  }

  .quick-links li {
    background-color: #4a4a4a;
    border-color: #666;
  }

  .quick-links li:hover {
    background-color: #5a5a5a;
  }

  .panel {
    background-color: #2f2f2f;
    border-left-color: #555;
  }

  .panel-header {
    background-color: #3a3a3a;
    border-bottom-color: #555;
  }

  .bookmark-item,
  .history-item {
    border-bottom-color: #444;
  }

  .bookmark-item:hover,
  .history-item:hover {
    background-color: #3a3a3a;
  }
}
