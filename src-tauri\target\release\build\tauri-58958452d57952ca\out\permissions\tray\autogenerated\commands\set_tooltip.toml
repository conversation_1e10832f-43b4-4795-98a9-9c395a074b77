# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-tooltip"
description = "Enables the set_tooltip command without any pre-configured scope."
commands.allow = ["set_tooltip"]

[[permission]]
identifier = "deny-set-tooltip"
description = "Denies the set_tooltip command without any pre-configured scope."
commands.deny = ["set_tooltip"]
