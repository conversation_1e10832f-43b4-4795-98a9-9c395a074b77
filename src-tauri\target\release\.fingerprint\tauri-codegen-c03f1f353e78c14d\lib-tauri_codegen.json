{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 18268707459763164491, "deps": [[3060637413840920116, "proc_macro2", false, 4450968399663963366], [3150220818285335163, "url", false, 5389458611647337435], [4899080583175475170, "semver", false, 11378919133499125916], [7170110829644101142, "json_patch", false, 13202743717636353198], [7392050791754369441, "ico", false, 13125239754543716505], [8319709847752024821, "uuid", false, 1147866204531567396], [9689903380558560274, "serde", false, 17446657524180286210], [9857275760291862238, "sha2", false, 4836948119445144015], [10806645703491011684, "thiserror", false, 3700247093477841424], [11050281405049894993, "tauri_utils", false, 3135135870016289477], [12687914511023397207, "png", false, 9909231255301668093], [13077212702700853852, "base64", false, 8517989230202560286], [14132538657330703225, "brotli", false, 6536127156072979561], [15367738274754116744, "serde_json", false, 6640214629378519811], [15622660310229662834, "walkdir", false, 17486384935986185728], [17990358020177143287, "quote", false, 7029536075333856339], [18149961000318489080, "syn", false, 9988623008240926651]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-c03f1f353e78c14d\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}