{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 1678142074430257323, "deps": [[376837177317575824, "build_script_build", false, 13916113782647382367], [4143744114649553716, "raw_window_handle", false, 9398976728429290834], [5986029879202738730, "log", false, 18140071090976075314], [10281541584571964250, "windows_sys", false, 2191643663505652543]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-fbedaeb8b8344906\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}