use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use tauri::State;
use tauri_plugin_opener::OpenerExt;

// 북마크 데이터 구조
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bookmark {
    pub title: String,
    pub url: String,
    pub created_at: String,
}

// 브라우저 히스토리 데이터 구조
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HistoryEntry {
    pub title: String,
    pub url: String,
    pub visited_at: String,
}

// 애플리케이션 상태
#[derive(Debug, Default)]
pub struct AppState {
    pub bookmarks: Mutex<Vec<Bookmark>>,
    pub history: Mutex<Vec<HistoryEntry>>,
    pub current_url: Mutex<String>,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn navigate_to_url(url: String, state: State<'_, AppState>) -> Result<String, String> {
    // URL 유효성 검사
    let parsed_url = url::Url::parse(&url).map_err(|e| format!("Invalid URL: {}", e))?;

    // 현재 URL 업데이트
    *state.current_url.lock().unwrap() = url.clone();

    // 히스토리에 추가
    let history_entry = HistoryEntry {
        title: parsed_url.host_str().unwrap_or("Unknown").to_string(),
        url: url.clone(),
        visited_at: chrono::Utc::now().to_rfc3339(),
    };

    state.history.lock().unwrap().push(history_entry);

    Ok(url)
}

#[tauri::command]
async fn get_page_content(url: String) -> Result<String, String> {
    let client = reqwest::Client::new();
    let response = client
        .get(&url)
        .send()
        .await
        .map_err(|e| format!("Failed to fetch page: {}", e))?;

    let content = response
        .text()
        .await
        .map_err(|e| format!("Failed to read page content: {}", e))?;

    Ok(content)
}

#[tauri::command]
async fn add_bookmark(title: String, url: String, state: State<'_, AppState>) -> Result<(), String> {
    let bookmark = Bookmark {
        title,
        url,
        created_at: chrono::Utc::now().to_rfc3339(),
    };

    state.bookmarks.lock().unwrap().push(bookmark);
    Ok(())
}

#[tauri::command]
async fn get_bookmarks(state: State<'_, AppState>) -> Result<Vec<Bookmark>, String> {
    let bookmarks = state.bookmarks.lock().unwrap().clone();
    Ok(bookmarks)
}

#[tauri::command]
async fn get_history(state: State<'_, AppState>) -> Result<Vec<HistoryEntry>, String> {
    let history = state.history.lock().unwrap().clone();
    Ok(history)
}

#[tauri::command]
async fn get_current_url(state: State<'_, AppState>) -> Result<String, String> {
    let url = state.current_url.lock().unwrap().clone();
    Ok(url)
}

#[tauri::command]
async fn open_url_in_browser(url: String, app: tauri::AppHandle) -> Result<(), String> {
    // Tauri opener 플러그인을 사용하여 시스템 기본 브라우저에서 URL 열기
    app.opener()
        .open_url(&url, None::<&str>)
        .map_err(|e| format!("Failed to open URL: {}", e))?;
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(AppState::default())
        .invoke_handler(tauri::generate_handler![
            greet,
            navigate_to_url,
            get_page_content,
            add_bookmark,
            get_bookmarks,
            get_history,
            get_current_url,
            open_url_in_browser
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
