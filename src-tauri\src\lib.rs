use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use tauri::{State, Manager, WebviewUrl, WebviewWindowBuilder};
use tauri_plugin_opener::OpenerExt;

// 북마크 데이터 구조
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Bookmark {
    pub title: String,
    pub url: String,
    pub created_at: String,
}

// 브라우저 히스토리 데이터 구조
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct HistoryEntry {
    pub title: String,
    pub url: String,
    pub visited_at: String,
}

// 애플리케이션 상태
#[derive(Debug, Default)]
pub struct AppState {
    pub bookmarks: Mutex<Vec<Bookmark>>,
    pub history: Mutex<Vec<HistoryEntry>>,
    pub current_url: Mutex<String>,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn navigate_to_url(url: String, state: State<'_, AppState>) -> Result<String, String> {
    // URL 유효성 검사
    let parsed_url = url::Url::parse(&url).map_err(|e| format!("Invalid URL: {}", e))?;

    // 현재 URL 업데이트
    *state.current_url.lock().unwrap() = url.clone();

    // 히스토리에 추가
    let history_entry = HistoryEntry {
        title: parsed_url.host_str().unwrap_or("Unknown").to_string(),
        url: url.clone(),
        visited_at: chrono::Utc::now().to_rfc3339(),
    };

    state.history.lock().unwrap().push(history_entry);

    Ok(url)
}

#[tauri::command]
async fn get_page_content(url: String) -> Result<String, String> {
    let client = reqwest::Client::builder()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        .build()
        .map_err(|e| format!("Failed to create client: {}", e))?;

    let response = client
        .get(&url)
        .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
        .header("Accept-Language", "en-US,en;q=0.5")
        .header("Accept-Encoding", "gzip, deflate")
        .header("Connection", "keep-alive")
        .header("Upgrade-Insecure-Requests", "1")
        .send()
        .await
        .map_err(|e| format!("Failed to fetch page: {}", e))?;

    let mut content = response
        .text()
        .await
        .map_err(|e| format!("Failed to read page content: {}", e))?;

    // X-Frame-Options 헤더를 우회하기 위해 HTML을 수정
    content = content.replace("X-Frame-Options", "X-Frame-Options-Disabled");
    content = content.replace("frame-ancestors", "frame-ancestors-disabled");

    Ok(content)
}

#[tauri::command]
async fn add_bookmark(title: String, url: String, state: State<'_, AppState>) -> Result<(), String> {
    let bookmark = Bookmark {
        title,
        url,
        created_at: chrono::Utc::now().to_rfc3339(),
    };

    state.bookmarks.lock().unwrap().push(bookmark);
    Ok(())
}

#[tauri::command]
async fn get_bookmarks(state: State<'_, AppState>) -> Result<Vec<Bookmark>, String> {
    let bookmarks = state.bookmarks.lock().unwrap().clone();
    Ok(bookmarks)
}

#[tauri::command]
async fn get_history(state: State<'_, AppState>) -> Result<Vec<HistoryEntry>, String> {
    let history = state.history.lock().unwrap().clone();
    Ok(history)
}

#[tauri::command]
async fn get_current_url(state: State<'_, AppState>) -> Result<String, String> {
    let url = state.current_url.lock().unwrap().clone();
    Ok(url)
}

#[tauri::command]
async fn open_url_in_browser(url: String, app: tauri::AppHandle) -> Result<(), String> {
    // Tauri opener 플러그인을 사용하여 시스템 기본 브라우저에서 URL 열기
    app.opener()
        .open_url(&url, None::<&str>)
        .map_err(|e| format!("Failed to open URL: {}", e))?;
    Ok(())
}

#[tauri::command]
async fn open_url_in_webview(url: String, app: tauri::AppHandle) -> Result<(), String> {
    // 앱 내에서 새 웹뷰 창을 생성하여 URL 열기
    let window_label = format!("webview_{}", chrono::Utc::now().timestamp());

    WebviewWindowBuilder::new(&app, &window_label, WebviewUrl::External(url.parse().map_err(|e| format!("Invalid URL: {}", e))?))
        .title(&format!("Browser - {}", url))
        .inner_size(1000.0, 700.0)
        .min_inner_size(600.0, 400.0)
        .build()
        .map_err(|e| format!("Failed to create webview: {}", e))?;

    Ok(())
}

#[tauri::command]
async fn get_proxy_url(url: String) -> Result<String, String> {
    // 간단한 프록시 URL 생성 (실제로는 더 복잡한 프록시 서버가 필요)
    // 여기서는 iframe에서 직접 로드를 시도
    Ok(url)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(AppState::default())
        .invoke_handler(tauri::generate_handler![
            greet,
            navigate_to_url,
            get_page_content,
            add_bookmark,
            get_bookmarks,
            get_history,
            get_current_url,
            open_url_in_browser,
            open_url_in_webview,
            get_proxy_url
        ])
        .setup(|app| {
            // 앱 시작 시 구글 웹뷰 창 자동 생성
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
                let _ = open_google_window(app_handle).await;
            });
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

async fn open_google_window(app: tauri::AppHandle) -> Result<(), String> {
    WebviewWindowBuilder::new(&app, "google_browser", WebviewUrl::External("https://www.google.com".parse().unwrap()))
        .title("Google - Web Browser")
        .inner_size(1200.0, 800.0)
        .min_inner_size(800.0, 600.0)
        .center()
        .build()
        .map_err(|e| format!("Failed to create Google window: {}", e))?;

    Ok(())
}
