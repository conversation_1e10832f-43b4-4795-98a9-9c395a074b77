{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 15039773433017454791, "deps": [[40386456601120721, "percent_encoding", false, 8330173392050763585], [442785307232013896, "tauri_runtime", false, 15999656449218192114], [1200537532907108615, "url<PERSON><PERSON>n", false, 2937405218787544394], [3150220818285335163, "url", false, 8166888441636404885], [4143744114649553716, "raw_window_handle", false, 9398976728429290834], [4341921533227644514, "muda", false, 10192843737893422776], [4919829919303820331, "serialize_to_javascript", false, 11439138760923443557], [5986029879202738730, "log", false, 18140071090976075314], [7752760652095876438, "tauri_runtime_wry", false, 17874725589725363090], [8539587424388551196, "webview2_com", false, 3052557753397640431], [9010263965687315507, "http", false, 11650626357901385786], [9228235415475680086, "tauri_macros", false, 18337941550278219408], [9538054652646069845, "tokio", false, 7669693513699817202], [9689903380558560274, "serde", false, 15479814090323490116], [9920160576179037441, "getrandom", false, 13510653366906299004], [10229185211513642314, "mime", false, 5889882709579962151], [10629569228670356391, "futures_util", false, 10446151027786071156], [10755362358622467486, "build_script_build", false, 11618994008688478091], [10806645703491011684, "thiserror", false, 5489843559512658793], [11050281405049894993, "tauri_utils", false, 14078234700842705457], [11989259058781683633, "dunce", false, 17502845036177328437], [12565293087094287914, "window_vibrancy", false, 1607960053733386739], [12986574360607194341, "serde_repr", false, 11784444089479698010], [13077543566650298139, "heck", false, 9932253229050198272], [13116089016666501665, "windows", false, 1127403627518652641], [13625485746686963219, "anyhow", false, 17767479543650335812], [15367738274754116744, "serde_json", false, 1540098585682478347], [16928111194414003569, "dirs", false, 12184092110490045269], [17155886227862585100, "glob", false, 8732721763287826903]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-acd0d6879d73bdb7\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}