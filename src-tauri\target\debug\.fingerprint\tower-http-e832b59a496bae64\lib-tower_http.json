{"rustc": 16591470773350601817, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 16809693924705985958, "deps": [[784494742817713399, "tower_service", false, 12907296848619203021], [1906322745568073236, "pin_project_lite", false, 8424272393616454842], [4121350475192885151, "iri_string", false, 5034053864170899768], [5695049318159433696, "tower", false, 3928122704557333387], [7712452662827335977, "tower_layer", false, 3991126197974912413], [7896293946984509699, "bitflags", false, 17237239352003576363], [9010263965687315507, "http", false, 11650626357901385786], [10629569228670356391, "futures_util", false, 10446151027786071156], [14084095096285906100, "http_body", false, 5284911468179628238], [16066129441945555748, "bytes", false, 17722555856617047069]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-e832b59a496bae64\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}