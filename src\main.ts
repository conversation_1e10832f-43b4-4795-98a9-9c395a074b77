import { invoke } from "@tauri-apps/api/core";

// 브라우저 상태 관리
interface BrowserState {
  currentUrl: string;
  history: string[];
  historyIndex: number;
  bookmarks: Bookmark[];
}

interface Bookmark {
  title: string;
  url: string;
  created_at: string;
}

interface HistoryEntry {
  title: string;
  url: string;
  visited_at: string;
}

class WebBrowser {
  private state: BrowserState;
  private urlInput: HTMLInputElement;
  private webview: HTMLIFrameElement;
  private webviewPlaceholder: HTMLElement;
  private loadingIndicator: HTMLElement;

  constructor() {
    this.state = {
      currentUrl: "",
      history: [],
      historyIndex: -1,
      bookmarks: []
    };

    // DOM 요소 초기화
    this.urlInput = document.getElementById("url-input") as HTMLInputElement;
    this.webview = document.getElementById("webview") as HTMLIFrameElement;
    this.webviewPlaceholder = document.getElementById("webview-placeholder") as HTMLElement;
    this.loadingIndicator = document.getElementById("loading-indicator") as HTMLElement;

    this.initializeEventListeners();
    this.setupWebviewEvents();
    this.loadBookmarks();
  }

  private setupWebviewEvents() {
    // iframe 로딩 이벤트 처리
    this.webview.addEventListener("load", () => {
      this.hideLoading();
      try {
        // iframe의 URL을 가져와서 주소 표시줄 업데이트
        const iframeUrl = this.webview.contentWindow?.location.href;
        if (iframeUrl && iframeUrl !== "about:blank") {
          this.urlInput.value = iframeUrl;
          this.state.currentUrl = iframeUrl;
        }
      } catch (error) {
        // CORS 때문에 iframe의 URL에 접근할 수 없는 경우
        console.log("Cannot access iframe URL due to CORS policy");
      }
    });

    this.webview.addEventListener("error", () => {
      this.hideLoading();
      this.showSuccessMessage("Failed to load page - some sites block iframe loading");
    });
  }

  private initializeEventListeners() {
    // 주소 표시줄 이벤트
    this.urlInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        this.navigateToUrl(this.urlInput.value);
      }
    });

    // Go 버튼
    document.getElementById("go-button")?.addEventListener("click", () => {
      this.navigateToUrl(this.urlInput.value);
    });

    // 네비게이션 버튼들
    document.getElementById("back-button")?.addEventListener("click", () => {
      this.goBack();
    });

    document.getElementById("forward-button")?.addEventListener("click", () => {
      this.goForward();
    });

    document.getElementById("refresh-button")?.addEventListener("click", () => {
      this.refresh();
    });

    document.getElementById("home-button")?.addEventListener("click", () => {
      this.goHome();
    });

    // 북마크 버튼
    document.getElementById("bookmark-button")?.addEventListener("click", () => {
      this.addBookmark();
    });

    // 북마크 목록 버튼
    document.getElementById("bookmarks-list-button")?.addEventListener("click", () => {
      this.toggleBookmarksPanel();
    });

    // 히스토리 버튼
    document.getElementById("history-button")?.addEventListener("click", () => {
      this.toggleHistoryPanel();
    });

    // 패널 닫기 버튼들
    document.getElementById("close-bookmarks-panel")?.addEventListener("click", () => {
      this.closePanel("bookmarks-panel");
    });

    document.getElementById("close-history-panel")?.addEventListener("click", () => {
      this.closePanel("history-panel");
    });

    // 퀵 링크 이벤트
    document.querySelectorAll(".quick-link").forEach(link => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        const url = (e.target as HTMLElement).getAttribute("data-url");
        if (url) {
          this.navigateToUrl(url);
        }
      });
    });
  }

  private async navigateToUrl(url: string) {
    if (!url) return;

    // URL 형식 정규화
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = "https://" + url;
    }

    try {
      this.showLoading();

      // Tauri 백엔드를 통해 URL 네비게이션
      await invoke("navigate_to_url", { url });

      // 새 웹뷰 창에서 URL 열기
      await invoke("open_url_in_webview", { url });

      // 주소 표시줄 업데이트
      this.urlInput.value = url;
      this.state.currentUrl = url;

      // 히스토리 업데이트 (중복 방지)
      if (this.state.history[this.state.historyIndex] !== url) {
        // 현재 위치 이후의 히스토리 제거 (새로운 경로)
        this.state.history = this.state.history.slice(0, this.state.historyIndex + 1);
        this.state.history.push(url);
        this.state.historyIndex = this.state.history.length - 1;
      }

      this.hideLoading();
      this.updateNavigationButtons();

      // 성공 메시지 표시
      this.showSuccessMessage(`Opened ${this.getPageTitle()} in new window`);

    } catch (error) {
      console.error("Navigation failed:", error);
      this.hideLoading();
      alert("Failed to navigate to URL: " + error);
    }
  }

  private goBack() {
    if (this.state.historyIndex > 0) {
      this.state.historyIndex--;
      const url = this.state.history[this.state.historyIndex];
      this.webview.src = url;
      this.urlInput.value = url;
      this.state.currentUrl = url;
      this.updateNavigationButtons();
      this.showSuccessMessage("Went back");
    }
  }

  private goForward() {
    if (this.state.historyIndex < this.state.history.length - 1) {
      this.state.historyIndex++;
      const url = this.state.history[this.state.historyIndex];
      this.webview.src = url;
      this.urlInput.value = url;
      this.state.currentUrl = url;
      this.updateNavigationButtons();
      this.showSuccessMessage("Went forward");
    }
  }

  private refresh() {
    if (this.state.currentUrl) {
      this.webview.src = this.state.currentUrl;
      this.showSuccessMessage("Page refreshed");
    }
  }

  private goHome() {
    this.webview.style.display = "none";
    this.webviewPlaceholder.style.display = "flex";
    this.urlInput.value = "";
    this.state.currentUrl = "";
    this.updateNavigationButtons();
    this.showSuccessMessage("Returned to home page");
  }

  private updateNavigationButtons() {
    const backButton = document.getElementById("back-button") as HTMLButtonElement;
    const forwardButton = document.getElementById("forward-button") as HTMLButtonElement;

    if (backButton) {
      backButton.disabled = this.state.historyIndex <= 0;
    }

    if (forwardButton) {
      forwardButton.disabled = this.state.historyIndex >= this.state.history.length - 1;
    }
  }

  private showLoading() {
    this.loadingIndicator.style.display = "block";
  }

  private hideLoading() {
    this.loadingIndicator.style.display = "none";
  }

  private showSuccessMessage(message: string) {
    // 임시 성공 메시지 표시
    const successDiv = document.createElement("div");
    successDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      z-index: 1000;
      font-size: 14px;
    `;
    successDiv.textContent = message;
    document.body.appendChild(successDiv);

    setTimeout(() => {
      document.body.removeChild(successDiv);
    }, 3000);
  }

  private async addBookmark() {
    if (!this.state.currentUrl) {
      alert("No page to bookmark");
      return;
    }

    const title = prompt("Bookmark title:", this.getPageTitle());
    if (title) {
      try {
        await invoke("add_bookmark", {
          title,
          url: this.state.currentUrl
        });

        this.loadBookmarks();
        alert("Bookmark added!");
      } catch (error) {
        console.error("Failed to add bookmark:", error);
        alert("Failed to add bookmark");
      }
    }
  }

  private getPageTitle(): string {
    // 간단한 제목 추출 (실제로는 웹뷰에서 제목을 가져와야 함)
    try {
      const url = new URL(this.state.currentUrl);
      return url.hostname;
    } catch {
      return this.state.currentUrl;
    }
  }

  private async loadBookmarks() {
    try {
      const bookmarks = await invoke("get_bookmarks") as Bookmark[];
      this.state.bookmarks = bookmarks;
      this.renderBookmarks();
    } catch (error) {
      console.error("Failed to load bookmarks:", error);
    }
  }

  private renderBookmarks() {
    const bookmarksList = document.getElementById("bookmarks-list");
    if (!bookmarksList) return;

    bookmarksList.innerHTML = "";

    this.state.bookmarks.forEach(bookmark => {
      const bookmarkElement = document.createElement("div");
      bookmarkElement.className = "bookmark-item";
      bookmarkElement.innerHTML = `
        <div class="bookmark-title">${bookmark.title}</div>
        <div class="bookmark-url">${bookmark.url}</div>
      `;

      bookmarkElement.addEventListener("click", () => {
        this.navigateToUrl(bookmark.url);
        this.closePanel("bookmarks-panel");
      });

      bookmarksList.appendChild(bookmarkElement);
    });
  }

  private async loadHistory() {
    try {
      const history = await invoke("get_history") as HistoryEntry[];
      this.renderHistory(history);
    } catch (error) {
      console.error("Failed to load history:", error);
    }
  }

  private renderHistory(history: HistoryEntry[]) {
    const historyList = document.getElementById("history-list");
    if (!historyList) return;

    historyList.innerHTML = "";

    history.reverse().forEach(entry => {
      const historyElement = document.createElement("div");
      historyElement.className = "history-item";
      historyElement.innerHTML = `
        <div class="history-title">${entry.title}</div>
        <div class="history-url">${entry.url}</div>
        <div class="history-time">${new Date(entry.visited_at).toLocaleString()}</div>
      `;

      historyElement.addEventListener("click", () => {
        this.navigateToUrl(entry.url);
        this.closePanel("history-panel");
      });

      historyList.appendChild(historyElement);
    });
  }

  private toggleBookmarksPanel() {
    const panel = document.getElementById("bookmarks-panel");
    if (panel) {
      panel.classList.toggle("open");
      if (panel.classList.contains("open")) {
        this.loadBookmarks();
      }
    }
  }

  private toggleHistoryPanel() {
    const panel = document.getElementById("history-panel");
    if (panel) {
      panel.classList.toggle("open");
      if (panel.classList.contains("open")) {
        this.loadHistory();
      }
    }
  }

  private closePanel(panelId: string) {
    const panel = document.getElementById(panelId);
    if (panel) {
      panel.classList.remove("open");
    }
  }
}

// 애플리케이션 초기화
window.addEventListener("DOMContentLoaded", () => {
  new WebBrowser();
});
