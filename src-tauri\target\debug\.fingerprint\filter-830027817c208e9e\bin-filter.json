{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 11240031315232193976, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[889364689474263285, "reqwest", false, 4683481744426019502], [2716489052144243376, "filter_lib", false, 965043408377960295], [2716489052144243376, "build_script_build", false, 7961487511349848767], [3150220818285335163, "url", false, 8166888441636404885], [3935545708480822364, "tauri_plugin_opener", false, 13155869186862328362], [9538054652646069845, "tokio", false, 7669693513699817202], [9689903380558560274, "serde", false, 15479814090323490116], [9897246384292347999, "chrono", false, 6190753969196142455], [10755362358622467486, "tauri", false, 2234279183662090554], [15367738274754116744, "serde_json", false, 1540098585682478347]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\filter-830027817c208e9e\\dep-bin-filter", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}