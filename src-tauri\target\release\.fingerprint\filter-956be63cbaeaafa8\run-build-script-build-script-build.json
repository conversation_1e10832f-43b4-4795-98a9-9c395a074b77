{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2716489052144243376, "build_script_build", false, 3585635706461364777], [10755362358622467486, "build_script_build", false, 6802830090495295321], [3935545708480822364, "build_script_build", false, 14010890657692248366]], "local": [{"RerunIfChanged": {"output": "release\\build\\filter-956be63cbaeaafa8\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}