{"$schema": "https://schema.tauri.app/config/2", "productName": "filter", "version": "0.1.0", "identifier": "com.filter.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": true, "windows": [{"title": "Web Browser", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}], "security": {"csp": null, "dangerousDisableAssetCspModification": true}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}